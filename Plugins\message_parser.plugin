import os
import time
import json
import threading
import traceback
from typing import Any, Dict, Optional, List
from datetime import datetime

from base_plugin import BasePlugin, HookResult, HookStrategy, MenuItemData, MenuItemType
from client_utils import (
    get_messages_controller, run_on_queue, send_message, get_last_fragment,
    get_user_config, send_request, RequestCallback, get_connections_manager
)
from markdown_utils import parse_markdown
from ui.settings import Header, Input, Divider, Switch, Selector, Text
from ui.bulletin import BulletinHelper
from ui.alert import AlertDialogBuilder
from android_utils import run_on_ui_thread, log

from java.util import Locale
from org.telegram.tgnet import TLRPC
from org.telegram.messenger import MessageObject, UserObject, ChatObject

__id__ = "MessageParser"
__name__ = "Message Parser"
__description__ = "Парсит сообщения чата и сохраняет их в .txt формат [.export, .parse] - Оптимизированная версия с защитой от rate limiting"
__author__ = "@mihai<PERSON><PERSON><PERSON>ski & @mishabotov"
__version__ = "1.1.0"
__min_version__ = "11.12.1"
__icon__ = "msg_download"

class LocalizationManager:
    strings = {
        "ru": {
            "SETTINGS_HEADER": "Настройки Message Parser",
            "ENABLE_SWITCH": "Включить парсер",
            "MESSAGE_COUNT_INPUT": "Количество сообщений",
            "MESSAGE_COUNT_SUBTEXT": "Сколько последних сообщений парсить (без ограничений)",
            "MAX_MESSAGE_LIMIT_INPUT": "Лимит сообщений",
            "MAX_MESSAGE_LIMIT_SUBTEXT": "Максимальное количество сообщений для парсинга (0 = без ограничений)",
            "INCLUDE_TIMESTAMPS": "Включить временные метки",
            "INCLUDE_TIMESTAMPS_SUBTEXT": "Добавлять время отправки к каждому сообщению",
            "INCLUDE_USERNAMES": "Включить имена пользователей",
            "INCLUDE_USERNAMES_SUBTEXT": "Добавлять имена отправителей к сообщениям",
            "FILE_FORMAT": "Формат файла",
            "SAVE_LOCATION": "Папка сохранения",
            "SAVE_LOCATION_SUBTEXT": "Путь для сохранения файлов (по умолчанию: Downloads/exteraGram/)",
            "PARSING_MESSAGE": "📄 Парсинг сообщений...",
            "NO_MESSAGES": "❌ Не найдено сообщений для парсинга.",
            "UNEXPECTED_ERROR": "❗ Произошла ошибка: {error}",
            "USAGE_INFO_TITLE": "Как использовать",
            "USAGE_INFO_TEXT": (
                "Команды плагина:\n\n"
                ".export - Экспорт последних сообщений в .txt файл\n"
                ".parse - Парсинг сообщений (аналог .export)\n"
                ".export 1000 - Экспорт определенного количества сообщений\n\n"
                "Плагин парсит сообщения в текущем чате и сохраняет их в .txt файл."
            ),
            "FILE_SAVED": "✅ Файл сохранен: {filename}",
            "SAVE_ERROR": "❌ Ошибка сохранения файла: {error}"
        },
        "en": {
            "SETTINGS_HEADER": "Message Parser Settings",
            "ENABLE_SWITCH": "Enable Parser",
            "MESSAGE_COUNT_INPUT": "Message Count",
            "MESSAGE_COUNT_SUBTEXT": "How many recent messages to parse (no limits)",
            "MAX_MESSAGE_LIMIT_INPUT": "Message Limit",
            "MAX_MESSAGE_LIMIT_SUBTEXT": "Maximum number of messages for parsing (0 = no limit)",
            "INCLUDE_TIMESTAMPS": "Include Timestamps",
            "INCLUDE_TIMESTAMPS_SUBTEXT": "Add sending time to each message",
            "INCLUDE_USERNAMES": "Include Usernames",
            "INCLUDE_USERNAMES_SUBTEXT": "Add sender names to messages",
            "FILE_FORMAT": "File Format",
            "SAVE_LOCATION": "Save Location",
            "SAVE_LOCATION_SUBTEXT": "Path for saving files (default: Downloads/exteraGram/)",
            "PARSING_MESSAGE": "📄 Parsing messages...",
            "NO_MESSAGES": "❌ No messages found for parsing.",
            "UNEXPECTED_ERROR": "❗ An error occurred: {error}",
            "USAGE_INFO_TITLE": "How to use",
            "USAGE_INFO_TEXT": (
                "Plugin commands:\n\n"
                ".export - Export recent messages to .txt file\n"
                ".parse - Parse messages (same as .export)\n"
                ".export 1000 - Export specific number of messages\n\n"
                "The plugin parses messages in current chat and saves them to .txt file."
            ),
            "FILE_SAVED": "✅ File saved: {filename}",
            "SAVE_ERROR": "❌ File save error: {error}"
        }
    }

    def __init__(self):
        self.language = Locale.getDefault().getLanguage()
        self.language = self.language if self.language in self.strings else "en"

    def get_string(self, key: str, **kwargs) -> str:
        string = self.strings[self.language].get(key, self.strings["en"].get(key, key))
        if kwargs:
            try:
                return string.format(**kwargs)
            except (KeyError, ValueError):
                return string
        return string

locali = LocalizationManager()

class MessageParserPlugin(BasePlugin):
    def __init__(self):
        super().__init__()
        self.progress_dialog: Optional[AlertDialogBuilder] = None

    def on_plugin_load(self):
        self.add_on_send_message_hook()
        self.log("Message Parser plugin loaded")

    def on_plugin_unload(self):
        if self.progress_dialog:
            run_on_ui_thread(lambda: self.progress_dialog.dismiss())
        self.log("Message Parser plugin unloaded")

    def _show_error_bulletin(self, key: str, **kwargs):
        message = locali.get_string(key).format(**kwargs)
        run_on_ui_thread(lambda: BulletinHelper.show_error(message))

    def _get_current_dialog_id(self) -> Optional[int]:
        """Получить ID текущего диалога"""
        try:
            fragment = get_last_fragment()
            if fragment and hasattr(fragment, 'getDialogId'):
                return fragment.getDialogId()
            elif fragment and hasattr(fragment, 'dialog_id'):
                return getattr(fragment, 'dialog_id')
            return None
        except Exception as e:
            self.log(f"Error getting dialog ID: {e}")
            return None

    def _get_topic_id_from_fragment(self) -> int:
        """Получить ID топика для форумов"""
        try:
            fragment = get_last_fragment()
            if fragment and hasattr(fragment, 'threadMessageId'):
                return getattr(fragment, 'threadMessageId', 0)
            return 0
        except Exception as e:
            self.log(f"Error getting topic ID: {e}")
            return 0

    def _fetch_message_history(self, dialog_id: int, limit: int, callback):
        """Получить историю сообщений из чата с пагинацией"""
        try:
            self.log(f"Starting to fetch {limit} messages")
            self._fetch_messages_paginated(dialog_id, limit, 0, [], {}, {}, callback)
        except Exception as e:
            self.log(f"Error in _fetch_message_history: {e}")
            callback(None, f"Ошибка: {str(e)}")

    def _fetch_messages_paginated(self, dialog_id: int, total_limit: int, offset_id: int,
                                 accumulated_messages: List, users: Dict, chats: Dict, callback,
                                 retry_count: int = 0, batch_number: int = 1):
        """Рекурсивно получать сообщения с пагинацией с оптимизацией"""
        try:
            # Определяем сколько сообщений запросить в этом запросе
            remaining = total_limit - len(accumulated_messages)
            if remaining <= 0:
                self.log(f"Reached target limit, returning {len(accumulated_messages)} messages")
                callback(accumulated_messages, None)
                return

            current_limit = min(100, remaining)  # Telegram API limit is 100 per request

            # Добавляем задержку между запросами для предотвращения rate limiting
            request_delay = self.get_setting("request_delay", 1.5)  # Секунды между запросами
            max_retries = self.get_setting("max_retries", 3)

            if batch_number > 1:  # Не задерживаем первый запрос
                self.log(f"Adding {request_delay}s delay before batch {batch_number}")
                # Используем run_on_queue с задержкой вместо threading.Timer
                delay_ms = int(request_delay * 1000)  # Конвертируем в миллисекунды
                run_on_queue(lambda: self._execute_request(
                    dialog_id, total_limit, offset_id, accumulated_messages, users, chats,
                    callback, retry_count, batch_number, current_limit
                ), delay_ms)
            else:
                self._execute_request(dialog_id, total_limit, offset_id, accumulated_messages,
                                    users, chats, callback, retry_count, batch_number, current_limit)

        except Exception as e:
            self.log(f"Error in _fetch_messages_paginated: {e}")
            if accumulated_messages:
                callback(accumulated_messages, None)
            else:
                callback(None, f"Ошибка: {str(e)}")

    def _execute_request(self, dialog_id: int, total_limit: int, offset_id: int,
                        accumulated_messages: List, users: Dict, chats: Dict, callback,
                        retry_count: int, batch_number: int, current_limit: int):
        """Выполнить запрос к API"""
        try:
            req = TLRPC.TL_messages_getHistory()
            req.peer = get_messages_controller().getInputPeer(dialog_id)
            req.offset_id = offset_id
            req.limit = current_limit
            req.add_offset = 0
            req.max_id = 0
            req.min_id = 0
            req.hash = 0

            def handle_response(response, error):
                try:
                    if error:
                        error_msg = error.text if hasattr(error, 'text') else str(error)
                        self.log(f"Error fetching messages (batch {batch_number}): {error_msg}")

                        # Обработка flood wait ошибок
                        if self._handle_flood_wait_error(error_msg, dialog_id, total_limit, offset_id,
                                                       accumulated_messages, users, chats, callback,
                                                       retry_count, batch_number):
                            return

                        # Обработка других ошибок с повторными попытками
                        max_retries = self.get_setting("max_retries", 3)
                        if retry_count < max_retries:
                            retry_delay = min(2 ** retry_count, 10)  # Exponential backoff, max 10 sec
                            self.log(f"Retrying in {retry_delay}s (attempt {retry_count + 1}/{max_retries})")
                            retry_delay_ms = int(retry_delay * 1000)
                            run_on_queue(lambda: self._fetch_messages_paginated(
                                dialog_id, total_limit, offset_id, accumulated_messages, users, chats,
                                callback, retry_count + 1, batch_number
                            ), retry_delay_ms)
                            return

                        if accumulated_messages:
                            # Возвращаем то что уже получили
                            callback(accumulated_messages, None)
                        else:
                            callback(None, f"Ошибка получения сообщений: {error_msg}")
                        return

                    if not response or not hasattr(response, 'messages'):
                        if accumulated_messages:
                            callback(accumulated_messages, None)
                        else:
                            callback(None, "Пустой ответ от сервера")
                        return

                    messages_count = response.messages.size()
                    self.log(f"Received {messages_count} messages in batch {batch_number} (offset_id: {offset_id})")

                    # Если получили 0 сообщений, значит достигли конца
                    if messages_count == 0:
                        self.log(f"No more messages available, returning {len(accumulated_messages)} messages")
                        callback(accumulated_messages, None)
                        return

                    # Обновляем пользователей и чаты
                    if hasattr(response, 'users') and response.users and response.users.size() > 0:
                        for i in range(response.users.size()):
                            try:
                                user = response.users.get(i)
                                if hasattr(user, 'id'):
                                    users[user.id] = user
                            except Exception as user_error:
                                self.log(f"Error processing user {i}: {user_error}")
                                continue

                    if hasattr(response, 'chats') and response.chats and response.chats.size() > 0:
                        for i in range(response.chats.size()):
                            try:
                                chat = response.chats.get(i)
                                if hasattr(chat, 'id'):
                                    chats[chat.id] = chat
                            except Exception as chat_error:
                                self.log(f"Error processing chat {i}: {chat_error}")
                                continue

                    # Обрабатываем сообщения из этого батча
                    batch_messages = []
                    last_message_id = offset_id

                    for i in range(messages_count):
                        msg = response.messages.get(i)
                        try:
                            if not hasattr(msg, 'message') or not msg.message or not msg.message.strip():
                                continue

                            # Пропускаем служебные сообщения
                            if hasattr(msg, 'action') and msg.action:
                                continue

                            # Получаем имя отправителя
                            sender_name = self._get_sender_name(msg, users, chats)

                            # Форматируем время
                            msg_time = self._format_message_time(msg)

                            # Берем полный текст сообщения
                            message_text = msg.message

                            batch_messages.append({
                                'sender': sender_name,
                                'text': message_text,
                                'time': msg_time,
                                'id': msg.id if hasattr(msg, 'id') else 0,
                                'date': msg.date if hasattr(msg, 'date') else 0
                            })

                            # Запоминаем ID последнего сообщения для следующего запроса
                            if hasattr(msg, 'id'):
                                last_message_id = msg.id

                        except Exception as msg_error:
                            self.log(f"Error processing message: {msg_error}")
                            continue

                    # Добавляем новые сообщения к накопленным
                    accumulated_messages.extend(batch_messages)
                    self.log(f"Processed {len(batch_messages)} messages in batch {batch_number}, total: {len(accumulated_messages)}")

                    # Проверяем нужно ли сохранить промежуточный результат
                    batch_save_interval = self.get_setting("batch_save_interval", 1000)
                    if batch_save_interval > 0 and len(accumulated_messages) % batch_save_interval == 0:
                        self._save_intermediate_batch(accumulated_messages, batch_number)

                    # Проверяем нужно ли получать еще сообщения
                    max_limit = self._get_max_message_limit()

                    # Если max_limit = 0, то без ограничений, продолжаем пока есть сообщения
                    # Если max_limit > 0, то ограничиваем этим значением
                    should_continue = True

                    if max_limit > 0 and len(accumulated_messages) >= max_limit:
                        # Достигли максимального лимита
                        final_messages = accumulated_messages[:max_limit]
                        self.log(f"Reached max limit, returning {len(final_messages)} messages")
                        callback(final_messages, None)
                        should_continue = False
                    elif len(accumulated_messages) >= total_limit:
                        # Достигли запрошенного количества
                        final_messages = accumulated_messages[:total_limit]
                        self.log(f"Reached requested limit, returning {len(final_messages)} messages")
                        callback(final_messages, None)
                        should_continue = False
                    elif len(batch_messages) == 0:
                        # Больше нет сообщений
                        self.log(f"No more messages available, returning {len(accumulated_messages)} messages")
                        callback(accumulated_messages, None)
                        should_continue = False

                    if should_continue:
                        # Запрашиваем следующий батч
                        self.log(f"Fetching next batch {batch_number + 1} with offset_id: {last_message_id}")
                        self._fetch_messages_paginated(dialog_id, total_limit, last_message_id,
                                                     accumulated_messages, users, chats, callback, 0, batch_number + 1)

                except Exception as response_error:
                    self.log(f"Error in handle_response: {response_error}")
                    if accumulated_messages:
                        callback(accumulated_messages, None)
                    else:
                        callback(None, f"Ошибка обработки ответа: {str(response_error)}")

            request_callback = RequestCallback(handle_response)
            send_request(req, request_callback)

        except Exception as e:
            self.log(f"Error in _execute_request: {e}")
            if accumulated_messages:
                callback(accumulated_messages, None)
            else:
                callback(None, f"Ошибка: {str(e)}")

    def _handle_flood_wait_error(self, error_msg: str, dialog_id: int, total_limit: int, offset_id: int,
                               accumulated_messages: List, users: Dict, chats: Dict, callback,
                               retry_count: int, batch_number: int) -> bool:
        """Обработка flood wait ошибок с экспоненциальной задержкой"""
        try:
            if "FLOOD_WAIT_" in error_msg:
                # Извлекаем время ожидания из ошибки
                import re
                wait_match = re.search(r'FLOOD_WAIT_(\d+)', error_msg)
                if wait_match:
                    wait_time = int(wait_match.group(1))
                    # Добавляем небольшой буфер к времени ожидания
                    wait_time += 2

                    max_wait_time = self.get_setting("max_flood_wait", 60)  # Максимальное время ожидания
                    if wait_time > max_wait_time:
                        self.log(f"Flood wait time {wait_time}s exceeds maximum {max_wait_time}s, aborting")
                        if accumulated_messages:
                            callback(accumulated_messages, None)
                        else:
                            callback(None, f"Превышено максимальное время ожидания: {wait_time}s")
                        return True

                    self.log(f"Flood wait detected, waiting {wait_time}s before retry (batch {batch_number})")

                    # Показываем пользователю информацию о задержке
                    run_on_ui_thread(lambda: BulletinHelper.show_info(
                        f"⏳ Ожидание {wait_time}с из-за ограничений API (батч {batch_number})"
                    ))

                    # Планируем повторный запрос после ожидания
                    wait_time_ms = int(wait_time * 1000)
                    run_on_queue(lambda: self._fetch_messages_paginated(
                        dialog_id, total_limit, offset_id, accumulated_messages, users, chats,
                        callback, retry_count, batch_number
                    ), wait_time_ms)
                    return True
            return False
        except Exception as e:
            self.log(f"Error handling flood wait: {e}")
            return False

    def _save_intermediate_batch(self, messages: List[Dict], batch_number: int):
        """Сохранить промежуточный батч сообщений"""
        try:
            if not self.get_setting("enable_batch_saving", False):
                return

            save_location = self.get_setting("save_location", "/storage/emulated/0/Download/exteraGram/")
            os.makedirs(save_location, exist_ok=True)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"chat_export_batch_{batch_number}_{timestamp}.txt"
            filepath = os.path.join(save_location, filename)

            formatted_content = self._format_messages_for_export(messages)

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(formatted_content)

            self.log(f"Intermediate batch {batch_number} saved: {filename}")

        except Exception as e:
            self.log(f"Error saving intermediate batch: {e}")

    def _get_sender_name(self, msg, users: Dict, chats: Dict) -> str:
        """Получить имя отправителя сообщения"""
        try:
            if not hasattr(msg, 'from_id') or not msg.from_id:
                return "Unknown"

            if hasattr(msg.from_id, 'user_id') and msg.from_id.user_id in users:
                user = users[msg.from_id.user_id]
                return self._get_user_display_name(user)
            elif hasattr(msg.from_id, 'chat_id') and msg.from_id.chat_id in chats:
                chat = chats[msg.from_id.chat_id]
                return chat.title if hasattr(chat, 'title') else f"Chat {chat.id}"
            elif hasattr(msg.from_id, 'channel_id') and msg.from_id.channel_id in chats:
                chat = chats[msg.from_id.channel_id]
                return chat.title if hasattr(chat, 'title') else f"Channel {chat.id}"
            else:
                return "Unknown"
        except Exception as e:
            self.log(f"Error getting sender name: {e}")
            return "Unknown"

    def _format_message_time(self, msg) -> str:
        """Форматировать время сообщения"""
        try:
            if hasattr(msg, 'date') and msg.date:
                return datetime.fromtimestamp(msg.date).strftime("%H:%M")
            return ""
        except Exception as e:
            self.log(f"Error formatting message time: {e}")
            return ""

    def _get_user_display_name(self, user) -> str:
        """Получить отображаемое имя пользователя"""
        try:
            if not user:
                return "Unknown"

            name_parts = []
            if hasattr(user, 'first_name') and user.first_name:
                name_parts.append(user.first_name)
            if hasattr(user, 'last_name') and user.last_name:
                name_parts.append(user.last_name)

            if name_parts:
                return " ".join(name_parts)
            elif hasattr(user, 'username') and user.username:
                return f"@{user.username}"
            else:
                return f"User {user.id}"
        except Exception as e:
            self.log(f"Error getting user display name: {e}")
            return "Unknown"

    def _format_messages_for_export(self, messages: List[Dict]) -> str:
        """Форматировать сообщения для экспорта в файл"""
        if not messages:
            return ""

        include_timestamps = self.get_setting("include_timestamps", True)
        include_usernames = self.get_setting("include_usernames", True)

        formatted_messages = []

        # Добавляем заголовок
        chat_info = self._get_chat_info()
        export_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        header = f"=== Экспорт сообщений чата ===\n"
        header += f"Чат: {chat_info}\n"
        header += f"Время экспорта: {export_time}\n"
        header += f"Количество сообщений: {len(messages)}\n"
        header += "=" * 40 + "\n\n"

        formatted_messages.append(header)

        # Сортируем сообщения по времени (от старых к новым)
        sorted_messages = sorted(messages, key=lambda x: x.get('date', 0))

        for msg in sorted_messages:
            parts = []

            if include_timestamps and msg['time']:
                parts.append(f"[{msg['time']}]")

            if include_usernames and msg['sender']:
                parts.append(f"{msg['sender']}:")

            parts.append(msg['text'])

            formatted_line = " ".join(parts) + "\n"
            formatted_messages.append(formatted_line)

        return "".join(formatted_messages)

    def _get_chat_info(self) -> str:
        """Получить информацию о текущем чате"""
        try:
            fragment = get_last_fragment()
            if fragment and hasattr(fragment, 'getCurrentChat'):
                chat = fragment.getCurrentChat()
                if chat and hasattr(chat, 'title'):
                    return chat.title
            elif fragment and hasattr(fragment, 'getCurrentUser'):
                user = fragment.getCurrentUser()
                if user:
                    return self._get_user_display_name(user)
            return "Unknown Chat"
        except Exception as e:
            self.log(f"Error getting chat info: {e}")
            return "Unknown Chat"

    def _save_messages_to_file(self, messages: List[Dict], chat_info: str) -> str:
        """Сохранить сообщения в файл"""
        try:
            # Получаем путь для сохранения
            save_location = self.get_setting("save_location", "/storage/emulated/0/Download/exteraGram/")

            # Создаем директорию если не существует
            os.makedirs(save_location, exist_ok=True)

            # Генерируем имя файла
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_chat_name = "".join(c for c in chat_info if c.isalnum() or c in (' ', '-', '_')).rstrip()
            safe_chat_name = safe_chat_name.replace(' ', '_')[:50]  # Ограничиваем длину

            filename = f"chat_export_{safe_chat_name}_{timestamp}.txt"
            filepath = os.path.join(save_location, filename)

            # Форматируем и сохраняем сообщения
            formatted_content = self._format_messages_for_export(messages)

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(formatted_content)

            self.log(f"Messages saved to: {filepath}")
            return filename

        except Exception as e:
            self.log(f"Error saving messages to file: {e}")
            raise e

    def on_send_message_hook(self, account: int, params: Any) -> HookResult:
        """Обработка команд плагина"""
        if not isinstance(params.message, str):
            return HookResult()

        message = params.message.strip()

        # Проверяем команды
        if message.startswith('.export') or message.startswith('.parse'):
            if not self.get_setting("enabled", True):
                params.message = "❌ Плагин отключен в настройках"
                return HookResult(strategy=HookStrategy.MODIFY, params=params)

            # Получаем ID диалога
            dialog_id = self._get_current_dialog_id()
            if not dialog_id:
                params.message = "❌ Не удалось определить текущий чат"
                return HookResult(strategy=HookStrategy.MODIFY, params=params)

            # Парсим количество сообщений из команды
            parts = message.split()
            message_count = None

            # Проверяем есть ли число в команде
            if len(parts) > 1 and parts[1].isdigit():
                try:
                    requested_count = int(parts[1])
                    max_limit = self._get_max_message_limit()
                    if max_limit == 0:  # Без ограничений
                        message_count = max(1, requested_count)
                    else:
                        message_count = max(1, min(max_limit, requested_count))
                    self.log(f"Using message count from command: {message_count}")
                except ValueError:
                    pass

            # Если в команде не указано число, берем из настроек
            if message_count is None:
                try:
                    config_count = int(self.get_setting("message_count", "500"))
                    max_limit = self._get_max_message_limit()
                    if max_limit == 0:  # Без ограничений
                        message_count = max(1, config_count)
                    else:
                        message_count = max(1, min(max_limit, config_count))
                    self.log(f"Using message count from settings: {message_count}")
                except (ValueError, TypeError):
                    message_count = 500
                    self.log(f"Using default message count: {message_count}")

            # Показываем индикатор загрузки
            BulletinHelper.show_info(locali.get_string("PARSING_MESSAGE"))

            # Создаем параметры для парсинга с поддержкой форумов
            parsing_params = self._prepare_parsing_params(params)

            # Запускаем парсинг в фоне
            run_on_queue(lambda: self._process_parsing(parsing_params, dialog_id, message_count))

            return HookResult(strategy=HookStrategy.CANCEL)

        return HookResult()

    def _prepare_parsing_params(self, params: Any) -> Any:
        """Подготовить параметры для парсинга с поддержкой форумов"""
        try:
            # Создаем копию параметров
            parsing_params = type('ParsingParams', (), {})()
            parsing_params.peer = params.peer

            # Добавляем поддержку replyToMsg если есть
            if hasattr(params, 'replyToMsg') and params.replyToMsg:
                parsing_params.replyToMsg = params.replyToMsg

            # Поддержка форумов - добавляем replyToTopMsg
            topic_id = self._get_topic_id_from_fragment()
            if topic_id > 0:
                parsing_params.replyToTopMsg = self._create_reply_to_top_message(topic_id, params.peer)
            elif hasattr(params, 'replyToTopMsg') and params.replyToTopMsg:
                parsing_params.replyToTopMsg = params.replyToTopMsg

            return parsing_params
        except Exception as e:
            self.log(f"Error preparing parsing params: {e}")
            return params

    def _create_reply_to_top_message(self, topic_id: int, peer_id: Any):
        """Создать replyToTopMsg для форумов"""
        try:
            if topic_id <= 0:
                return None

            reply_message = TLRPC.TL_message()
            reply_message.message = ""
            reply_message.id = topic_id
            reply_message.peer_id = get_messages_controller().getPeer(peer_id)

            account = get_user_config().selectedAccount
            reply_to_top_msg = MessageObject(account, reply_message, False, False)

            return reply_to_top_msg
        except Exception as e:
            self.log(f"Error creating replyToTopMsg: {e}")
            return None

    def _process_parsing(self, params: Any, dialog_id: int, message_count: int):
        """Обработка парсинга в фоновом потоке"""
        try:
            def handle_messages(messages, error):
                try:
                    if error:
                        self._send_error_message(params, error)
                        return

                    if not messages:
                        self._send_error_message(params, locali.get_string("NO_MESSAGES"))
                        return

                    # Проверяем минимальное количество сообщений
                    if len(messages) < 1:
                        self._send_error_message(params, "❌ Нет сообщений для экспорта")
                        return

                    # Получаем информацию о чате
                    chat_info = self._get_chat_info()

                    # Сохраняем сообщения в файл
                    try:
                        filename = self._save_messages_to_file(messages, chat_info)
                        success_message = locali.get_string("FILE_SAVED", filename=filename)
                        self._send_success_message(params, success_message, len(messages))

                        # Показываем уведомление об успехе
                        run_on_ui_thread(lambda: BulletinHelper.show_file_saved_to_downloads("TXT", 1))

                    except Exception as save_error:
                        error_message = locali.get_string("SAVE_ERROR", error=str(save_error))
                        self._send_error_message(params, error_message)

                except Exception as handle_error:
                    self.log(f"Error in handle_messages: {handle_error}")
                    self._send_error_message(params, f"Ошибка обработки: {str(handle_error)}")

            # Получаем сообщения
            self._fetch_message_history(dialog_id, message_count, handle_messages)

        except Exception as e:
            self.log(f"Error in _process_parsing: {e}")
            self._send_error_message(params, locali.get_string("UNEXPECTED_ERROR", error=str(e)))

    def _get_max_message_limit(self) -> int:
        """Получить максимальный лимит сообщений из настроек"""
        try:
            limit = int(self.get_setting("max_message_limit", "0"))
            return max(0, limit)  # 0 означает без ограничений
        except (ValueError, TypeError):
            return 0  # По умолчанию без ограничений

    def _send_success_message(self, params: Any, success_text: str, message_count: int):
        """Отправить сообщение об успехе"""
        try:
            # Форматируем сообщение с markdown
            full_message = f"📄 **Экспорт завершен**\n\n✅ **Файл сохранен:** `{success_text.replace('✅ Файл сохранен: ', '')}`\n\n📊 **Обработано сообщений:** `{message_count}`"

            message_payload = {
                "peer": params.peer
            }

            # Добавляем reply если есть
            if hasattr(params, 'replyToMsg') and params.replyToMsg:
                message_payload["replyToMsg"] = params.replyToMsg
            if hasattr(params, 'replyToTopMsg') and params.replyToTopMsg:
                message_payload["replyToTopMsg"] = params.replyToTopMsg

            # Парсим markdown и отправляем с форматированием
            try:
                parsed = parse_markdown(full_message)
                message_payload["message"] = parsed.text
                message_payload["entities"] = [entity.to_tlrpc_object() for entity in parsed.entities] if parsed.entities else None
                send_message(message_payload)
            except Exception as parse_error:
                self.log(f"Error parsing markdown, sending plain text: {parse_error}")
                # Fallback - отправляем без форматирования
                clean_message = full_message.replace('**', '').replace('`', '')
                message_payload["message"] = clean_message
                message_payload["entities"] = None
                send_message(message_payload)

        except Exception as e:
            self.log(f"Error sending success message: {e}")
            run_on_ui_thread(lambda: BulletinHelper.show_success(success_text))

    def _send_error_message(self, params: Any, error_text: str):
        """Отправить сообщение об ошибке"""
        try:
            message_payload = {
                "peer": params.peer,
                "message": error_text
            }

            # Добавляем reply если есть
            if hasattr(params, 'replyToMsg') and params.replyToMsg:
                message_payload["replyToMsg"] = params.replyToMsg
            if hasattr(params, 'replyToTopMsg') and params.replyToTopMsg:
                message_payload["replyToTopMsg"] = params.replyToTopMsg

            send_message(message_payload)
        except Exception as e:
            self.log(f"Error sending error message: {e}")
            run_on_ui_thread(lambda: BulletinHelper.show_error(error_text))

    def _handle_show_info_alert_click(self, view):
        """Показать информацию о плагине"""
        try:
            title = locali.get_string("USAGE_INFO_TITLE")
            text = locali.get_string("USAGE_INFO_TEXT")

            fragment = get_last_fragment()
            if not fragment or not fragment.getParentActivity():
                return
            context = fragment.getParentActivity()

            builder = AlertDialogBuilder(context, AlertDialogBuilder.ALERT_TYPE_MESSAGE)
            builder.set_title(title)
            builder.set_message(text)
            builder.set_positive_button("Закрыть", lambda d, w: builder.dismiss())
            builder.set_cancelable(True)
            run_on_ui_thread(builder.show)
        except Exception as e:
            self.log(f"Error showing info alert: {e}")

    def create_settings(self) -> List[Any]:
        """Создать настройки плагина"""
        max_limit = self._get_max_message_limit()
        return [
            Header(text=locali.get_string("SETTINGS_HEADER")),
            Switch(
                key="enabled",
                text=locali.get_string("ENABLE_SWITCH"),
                icon="msg_download",
                default=True
            ),
            Divider(),
            Header(text="Настройки парсинга"),
            Input(
                key="message_count",
                text=locali.get_string("MESSAGE_COUNT_INPUT"),
                icon="msg_voicechat_solar",
                default="500",
                subtext=locali.get_string("MESSAGE_COUNT_SUBTEXT")
            ),
            Input(
                key="max_message_limit",
                text=locali.get_string("MAX_MESSAGE_LIMIT_INPUT"),
                icon="msg_premium_limits",
                default="0",
                subtext=locali.get_string("MAX_MESSAGE_LIMIT_SUBTEXT")
            ),
            Divider(),
            Header(text="Оптимизация производительности"),
            Input(
                key="request_delay",
                text="Задержка между запросами",
                icon="msg_timer",
                default="1.5",
                subtext="Секунды между API запросами (рекомендуется 1-2 сек для предотвращения rate limiting)"
            ),
            Input(
                key="max_retries",
                text="Максимум повторных попыток",
                icon="msg_retry",
                default="3",
                subtext="Количество попыток при ошибках (0-10)"
            ),
            Input(
                key="max_flood_wait",
                text="Максимальное время ожидания",
                icon="msg_clock",
                default="60",
                subtext="Максимальное время ожидания при flood wait (секунды)"
            ),
            Input(
                key="batch_save_interval",
                text="Интервал сохранения батчей",
                icon="msg_saved",
                default="0",
                subtext="Сохранять промежуточные файлы каждые N сообщений (0 = отключено)"
            ),
            Switch(
                key="enable_batch_saving",
                text="Включить сохранение батчей",
                subtext="Сохранять промежуточные результаты во время парсинга",
                icon="msg_download",
                default=False
            ),
            Divider(),
            Header(text="Формат экспорта"),
            Switch(
                key="include_timestamps",
                text=locali.get_string("INCLUDE_TIMESTAMPS"),
                subtext=locali.get_string("INCLUDE_TIMESTAMPS_SUBTEXT"),
                icon="msg_calendar",
                default=True
            ),
            Switch(
                key="include_usernames",
                text=locali.get_string("INCLUDE_USERNAMES"),
                subtext=locali.get_string("INCLUDE_USERNAMES_SUBTEXT"),
                icon="msg_user",
                default=True
            ),
            Input(
                key="save_location",
                text=locali.get_string("SAVE_LOCATION"),
                icon="msg_folder",
                default="/storage/emulated/0/Download/exteraGram/",
                subtext=locali.get_string("SAVE_LOCATION_SUBTEXT")
            ),
            Divider(),
            Text(
                text=locali.get_string("USAGE_INFO_TITLE"),
                icon="msg_info",
                on_click=self._handle_show_info_alert_click
            ),
        ]
