import os
import json
from base_plugin import <PERSON><PERSON>lug<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Hook
from ui.settings import <PERSON><PERSON>, <PERSON><PERSON>, Di<PERSON>r, Selector
from android_utils import log, run_on_ui_thread
from client_utils import get_last_fragment
from hook_utils import find_class
from ui.alert import AlertDialogBuilder
from ui.bulletin import BulletinHelper

from org.telegram.tgnet import TLRPC
from org.telegram.messenger import MessageObject, ApplicationLoader
from java.util import ArrayList
from java.lang import Boolean

__id__ = "spoiler_remover"
__name__ = "Spoiler Remover"
__description__ = "Убирает спойлеры с текста и медиа. Имеет два режима: визуальный (обратимый) и перманентный (необратимый)."
__author__ = "@JasonVurhyz"
__version__ = "1.0.2"
__min_version__ = "11.12.0"

MODE_VISUAL = 0
MODE_PERMANENT = 1

class SpoilerCellHook(MethodHook):
    def __init__(self, plugin_instance: 'SpoilerRemoverUnifiedPlugin'):
        self.plugin = plugin_instance

    def before_hooked_method(self, param):
        try:
            if not self.plugin.get_setting("enabled", True):
                return
            
            message_object: MessageObject = param.args[0]
            if not message_object:
                return

            if self.plugin._process_message(message_object.messageOwner):
                message_object.forceUpdate = True
                message_object.resetLayout()
        except Exception as e:
            pass

class SpoilerRemoverUnifiedPlugin(BasePlugin):
    def __init__(self):
        super().__init__()
        self.hook_ref = None
        self.is_loaded = False
        self.config_dir = None
        self.config_path = None

    def _init_config_path(self):
        if self.config_path:
            return
        try:
            base_dir = ApplicationLoader.getFilesDirFixed()
            self.config_dir = os.path.join(str(base_dir), "spoiler_remover_data")
            if not os.path.exists(self.config_dir):
                os.makedirs(self.config_dir)
            self.config_path = os.path.join(self.config_dir, "spoiler_config.json")
        except Exception as e:
            pass

    def on_plugin_load(self):
        if self.is_loaded:
            return
        
        self.is_loaded = True
        self._init_config_path()

        first_run_needed = True
        if self.config_path and os.path.exists(self.config_path):
            try:
                with open(self.config_path, 'r') as f:
                    config_data = json.load(f)
                if config_data.get("first_run_completed"):
                    first_run_needed = False
            except Exception:
                pass

        if first_run_needed:
            run_on_ui_thread(self.show_first_run_dialog)
        else:
            self._apply_hooks_based_on_mode()

    def on_plugin_unload(self):
        self._remove_all_hooks()
        self.is_loaded = False

    def show_first_run_dialog(self):
        fragment = get_last_fragment()
        if not fragment or not fragment.getParentActivity():
            return

        builder = AlertDialogBuilder(fragment.getParentActivity())
        builder.set_title("Spoiler Remover: Выберите режим")
        builder.set_message(
            "Плагин может работать в двух режимах:\n\n"
            "🔹 Визуальный (рекомендуется)\n"
            "Спойлеры скрываются только при отображении. Безопасно и обратимо.\n\n"
            "🔸 Перманентный\n"
            "Спойлеры удаляются из новых сообщений навсегда (локально). Необратимо."
        )

        def on_mode_selected(dialog, which):
            mode = MODE_VISUAL if which == 0 else MODE_PERMANENT
            self.set_setting("operating_mode", mode)
            if self.config_path:
                try:
                    with open(self.config_path, 'w') as f:
                        json.dump({"first_run_completed": True}, f)
                except Exception as e:
                    pass

            self._apply_hooks_based_on_mode()
            
            mode_name = "Визуальный" if mode == MODE_VISUAL else "Перманентный"
            BulletinHelper.show_success(f"Выбран режим: {mode_name}")
            dialog.dismiss()

        builder.set_items(
            ["Визуальный (рекомендуется)", "Перманентный"],
            on_mode_selected
        )
        builder.set_cancelable(False)
        builder.show()

    def _apply_hooks_based_on_mode(self):
        self._remove_all_hooks()
        
        mode = self.get_setting("operating_mode", MODE_VISUAL)

        if mode == MODE_PERMANENT:
            self.add_hook("TL_updateNewMessage")
            self.add_hook("TL_updateNewChannelMessage")
        else:
            try:
                ChatMessageCellClass = find_class("org.telegram.ui.Cells.ChatMessageCell").getClass()
                target_method = ChatMessageCellClass.getDeclaredMethod(
                    "setMessageObject", 
                    MessageObject, MessageObject.GroupedMessages, Boolean.TYPE, Boolean.TYPE, Boolean.TYPE
                )
                self.hook_ref = self.hook_method(target_method, SpoilerCellHook(self))
            except Exception as e:
                pass

        self._schedule_ui_refresh()

    def _remove_all_hooks(self):
        self.remove_hook("TL_updateNewMessage")
        self.remove_hook("TL_updateNewChannelMessage")
        
        if self.hook_ref:
            self.unhook_method(self.hook_ref)
            self.hook_ref = None

    def on_update_hook(self, update_name: str, account: int, update: TLRPC.Update) -> HookResult:
        if not self.get_setting("enabled", True):
            return HookResult()

        message = getattr(update, 'message', None)
        if not message:
            return HookResult()

        if self._process_message(message):
            update.message = message
            return HookResult(strategy=HookStrategy.MODIFY, update=update)

        return HookResult()

    def _process_message(self, message: TLRPC.Message) -> bool:
        was_modified = False

        if self.get_setting("remove_from_text", True):
            if hasattr(message, 'entities') and message.entities and not message.entities.isEmpty():
                new_entities = ArrayList()
                is_modified = False
                for entity in message.entities.toArray():
                    if isinstance(entity, TLRPC.TL_messageEntitySpoiler):
                        is_modified = True
                    else:
                        new_entities.add(entity)
                if is_modified:
                    message.entities = new_entities
                    was_modified = True

        if self.get_setting("remove_from_media", True):
            if hasattr(message, 'media') and message.media:
                if hasattr(message.media, 'spoiler') and message.media.spoiler:
                    message.media.spoiler = False
                    was_modified = True
        
        return was_modified

    def _schedule_ui_refresh(self):
        def refresh_chat_view():
            fragment = get_last_fragment()
            if fragment and hasattr(fragment, 'getFragmentView'):
                view = fragment.getFragmentView()
                if view and hasattr(view, 'invalidate'):
                    view.invalidate()
        
        run_on_ui_thread(refresh_chat_view)

    def create_settings(self):
        return [
            Header(text="Основные настройки"),
            Switch(
                key="remove_from_text",
                text="Убирать спойлеры с текста",
                default=True,
                icon="msg_photo_text2"
            ),
            Switch(
                key="remove_from_media",
                text="Убирать спойлеры с медиа",
                default=True,
                icon="msg_photos"
            ),
            Divider(),
            Header(text="Режим работы"),
            Selector(
                key="operating_mode",
                text="Режим работы",
                items=["Визуальный (рекомендуется)", "Перманентный (необратимо)"],
                default=MODE_VISUAL,
                icon="msg_settings_14"
            ),
            Divider(text="Визуальный - обратимый, Перманентный - нет.\nПосле выбора перезапустите приложение!")
        ]