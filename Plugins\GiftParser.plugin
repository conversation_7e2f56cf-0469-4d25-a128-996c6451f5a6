from base_plugin import BasePlugin, Hook<PERSON><PERSON><PERSON>, HookStrategy
from cactuslib import CactusUtils
import requests, time, socket
from ui.settings import Header, Input, Text, Switch

__id__ = "gift_parser"
__name__ = "Gift Parser"
__description__ = "Пропиши команду .gift и получи актуальную информацию о телеграм подарках."
__author__ = "@urlEncoder & @wv_dev"
__version__ = "1.1.0"
__icon__ = "SuicideCode/0"

PREMIUM_EMOJI_MAP = {
    "💝": "5192879906295397710",
    "🧸": "5206502842478638898",
    "🎁": "5203996991054432397",
    "🌹": "5440911110838425969",
    "🎂": "5370999492914976897",
    "💐": "5190661263629243818",
    "🚀": "5445284980978621387",
    "🍾": "5370900768796711127",
    "🏆": "5409008750893734809",
    "💍": "5402100905883488232",
    "💎": "5471952986970267163",
    "⭐": "5463289097336405244",
    "📥": "5443127283898405358",
    "📶": "5980965624396910678"
}

SERVERS = {
    "1": "**************",
    "2": "**************",
    "3": "***************",
    "4": "**************",
    "5": "*************"
}

class GiftParser(CactusUtils.Plugin):
    def on_plugin_load(self):
        self.add_on_send_message_hook()
    
    def create_settings(self):
        return [
            Header(text="Основные настройки"),
            Input(
                key="bot_token",
                text="Bot Token",
                default="7126649166:AAFbs7hXJOXMLiTsyxRhbcnDWDxPT9SRN-U",
                subtext="Токен бота для запроса подарков",
                icon="msg_bot"
            ),
            Input(
                key="dc_number",
                text="DC [1-5]",
                default="2",
                subtext="Номер DC",
                icon="msg_calendar2"
            ),
            Input(
                key="command",
                text="Команда",
                default=".gift",
                subtext="Основная команда плагина",
                icon="msg_edit_solar"
            ),
            Switch(
                key="use_premium_emoji",
                text="Использовать премиум эмодзи",
                default=False,
                icon="filled_giveaway_premium"
            )
        ]
    
    def get_gifts(self):
        try:
            bot_token = self.get_setting("bot_token")
            if not bot_token:
                return None, "Токен бота не указан, получите его в @BotFather и укажите его в настройках плагина "
            
            response = requests.get(
                f"https://api.telegram.org/bot{bot_token}/getAvailableGifts",
                timeout=10
            )
            data = response.json()
            return data['result']['gifts'], None
            
        except Exception as e:
            return None, f"Ошибка: {str(e)}"
    
    def replace_with_premium(self, emoji):
        if not self.get_setting("use_premium_emoji", True):
            return emoji
        return f"![{emoji}]({PREMIUM_EMOJI_MAP.get(emoji, emoji)})"
    
    def ping_server(self, server_ip):
        try:
            start = time.time()
            with socket.create_connection((server_ip, 443), timeout=1):
                return round((time.time() - start) * 1000)
        except:
            return "N/A"
    
    def format_message(self, gifts):
        grouped = {}
        for gift in gifts:
            stars = gift['star_count']
            emoji = gift['sticker']['emoji']
            premium_emoji = self.replace_with_premium(emoji)
            grouped.setdefault(stars, []).append(premium_emoji)
        
        message = f"{self.replace_with_premium('📥')} *Available gifts:*\n"
        
        gift_lines = []
        for stars, emojis in sorted(grouped.items()):
            star_emoji = self.replace_with_premium("⭐")
            gift_lines.append(f"{' '.join(emojis)} | *{stars}* [stars](tg://stars) {star_emoji}")
        
        message += "> " + "\n> ".join(gift_lines) + "\n\n"
        
        dc_number = self.get_setting("dc_number", "2")
        server_ip = SERVERS.get(dc_number, "**************")
        ping = self.ping_server(server_ip)
        
        message += f"> {self.replace_with_premium('📶')} *Ping: {ping} ms*"
        message += f"\n>📠 *[Gift Parser](http://t.me/SuicideCode) {__version__}*"
        
        return message
    
    def on_send_message_hook(self, account, params):
        if not hasattr(params, 'message') or not isinstance(params.message, str):
            return HookResult()

        cmd = self.get_setting("command", ".gift").strip()
        if not params.message.strip().lower().startswith(cmd.lower()):
            return HookResult()

        gifts, error = self.get_gifts()
        if error:
            self.utils.send_message(peer=params.peer, text=error)
            return HookResult(strategy=HookStrategy.CANCEL)
        
        text = self.format_message(gifts)
        self.utils.send_message(
            peer=params.peer,
            text=text,
            parse_message=True,
            parse_mode="MARKDOWN"
        )
        return HookResult(strategy=HookStrategy.CANCEL)